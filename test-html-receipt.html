<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test HTML Receipt</title>
    <script type="module">
        // Mock data for testing
        const mockTransaction = {
            id: "test-123",
            wholesalerTransactionId: "TXN-2024-001",
            movementDate: "2024-01-15T10:30:00Z",
            totalAmount: 1500.00,
            interests: 75.00,
            IVAAmount: 240.00,
            totalAmountWithInterests: 1815.00,
            paymentDate: "2024-01-30T23:59:59Z",
            cornerStoreId: "store-456",
            products: [],
            wholesalerUserId: "user-789",
            status: "completed"
        };

        const mockReceiptData = {
            transaction: mockTransaction,
            businessName: "Propaga",
            businessAddress: "Av. Principal 123, Ciudad",
            businessPhone: "Tel: (*************"
        };

        // Mock utility functions
        function stringToLongDateFormatted(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('es-ES', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function numberToCurrencyString(amount) {
            return new Intl.NumberFormat('es-MX', {
                style: 'currency',
                currency: 'MXN'
            }).format(amount);
        }

        // Simplified version of formatHtmlReceipt for testing
        function formatHtmlReceipt(receiptData) {
            const { transaction, businessName = 'Propaga', businessAddress, businessPhone } = receiptData;

            const centerText = (text) => {
                return `<div class="center-text">${text}</div>`;
            };

            const formatTwoColumns = (left, right) => {
                return `<div class="two-columns"><span class="left-col">${left}</span><span class="right-col">${right}</span></div>`;
            };

            const separator = '<div class="separator">================================</div>';
            const dashLine = '<div class="dash-line">--------------------------------</div>';

            let receiptHtml = `
        <div class="receipt-container">
            <div class="receipt-content">`;

            // Business header
            receiptHtml += '<div class="business-info">';
            receiptHtml += '<div style="margin: 4px 0;"></div>';
            receiptHtml += centerText(businessName);
            if (businessAddress) {
                receiptHtml += centerText(businessAddress);
            }
            if (businessPhone) {
                receiptHtml += centerText(businessPhone);
            }
            receiptHtml += '<div style="margin: 4px 0;"></div>';
            receiptHtml += '<div class="transaction-title">' + centerText('RECIBO DE TRANSACCION') + '</div>';
            receiptHtml += separator;
            receiptHtml += '<div style="margin: 4px 0;"></div>';
            receiptHtml += '</div>';

            // Transaction details
            receiptHtml += formatTwoColumns('Referencia:', transaction.wholesalerTransactionId);
            receiptHtml += formatTwoColumns('Fecha:', stringToLongDateFormatted(transaction.movementDate));
            receiptHtml += '<div style="margin: 4px 0;"></div>';
            receiptHtml += dashLine;
            receiptHtml += '<div class="section-title">' + centerText('DETALLE DE COMPRA') + '</div>';
            receiptHtml += dashLine;

            // Purchase details
            receiptHtml += formatTwoColumns('Monto base:', numberToCurrencyString(transaction.totalAmount));

            if (transaction.interests > 0) {
                receiptHtml += formatTwoColumns('Intereses:', numberToCurrencyString(transaction.interests));
            }

            if (transaction.IVAAmount > 0) {
                receiptHtml += formatTwoColumns('IVA:', numberToCurrencyString(transaction.IVAAmount));
            }

            receiptHtml += dashLine;
            receiptHtml += '<div class="total-row">' + formatTwoColumns(
                'TOTAL:',
                numberToCurrencyString(transaction.totalAmountWithInterests || transaction.totalAmount)
            ) + '</div>';
            receiptHtml += separator;
            receiptHtml += '<div style="margin: 4px 0;"></div>';

            // Payment information
            receiptHtml += '<div class="payment-info">';
            receiptHtml += '<div class="section-title">' + centerText('INFORMACION DE PAGO') + '</div>';
            receiptHtml += dashLine;
            receiptHtml += formatTwoColumns('Plazo:', '15 dias');
            receiptHtml += formatTwoColumns('Fecha limite:', stringToLongDateFormatted(transaction.paymentDate));
            receiptHtml += '<div style="margin: 4px 0;"></div>';
            receiptHtml += '<div class="footer-text">' + centerText('El cliente pagará después') + '</div>';
            receiptHtml += '<div class="footer-text">' + centerText('en el portal de Pagos de Propaga') + '</div>';
            receiptHtml += '<div style="margin: 4px 0;"></div>';
            receiptHtml += '</div>';

            // Footer
            receiptHtml += separator;
            receiptHtml += '<div class="footer-text">' + centerText('Gracias por su compra') + '</div>';
            receiptHtml += '<div class="footer-text">' + centerText('Conserve este recibo') + '</div>';
            receiptHtml += '<div class="final-spacing"></div>';

            receiptHtml += `
            </div>
        </div>`;

            return receiptHtml;
        }

        // Generate and display the receipt
        document.addEventListener('DOMContentLoaded', function() {
            const receiptContainer = document.getElementById('receipt-container');
            const htmlReceipt = formatHtmlReceipt(mockReceiptData);
            receiptContainer.innerHTML = htmlReceipt;
        });
    </script>
    <style>
        @media print {
            @page {
                size: 58mm auto;
                margin: 0;
            }
            body {
                margin: 0;
                padding: 0;
            }
            .receipt-container {
                width: 58mm !important;
                max-width: 58mm !important;
                margin: 0 !important;
                padding: 2mm !important;
                box-shadow: none !important;
                border: none !important;
            }
        }

        body {
            margin: 0;
            padding: 20px;
            background-color: #e8e8e8;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
        }

        .receipt-container {
            width: 320px;
            max-width: 320px;
            background: linear-gradient(135deg, #f8f8f8 0%, #f5f5f5 100%);
            border: 2px dotted #ccc;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }

        .receipt-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 1px 1px, rgba(0,0,0,0.02) 1px, transparent 0);
            background-size: 8px 8px;
            pointer-events: none;
        }

        .receipt-content {
            position: relative;
            z-index: 1;
            font-size: 11px;
            line-height: 1.1;
            color: #2a2a2a;
            letter-spacing: 0.3px;
        }

        .center-text {
            text-align: center;
            margin: 2px 0;
            font-weight: 500;
        }

        .two-columns {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 1px 0;
            min-height: 12px;
        }

        .left-col {
            flex: 1;
            text-align: left;
            padding-right: 4px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .right-col {
            text-align: right;
            white-space: nowrap;
            font-weight: 600;
        }

        .separator {
            text-align: center;
            margin: 4px 0;
            font-weight: bold;
            letter-spacing: -0.5px;
            color: #444;
        }

        .dash-line {
            text-align: center;
            margin: 3px 0;
            color: #666;
            letter-spacing: -0.5px;
        }

        .business-info .center-text {
            font-weight: 600;
            font-size: 12px;
        }

        .transaction-title {
            font-weight: bold;
            font-size: 11px;
            margin: 6px 0;
        }

        .section-title {
            font-weight: bold;
            font-size: 10px;
            margin: 4px 0;
        }

        .total-row {
            font-weight: bold;
            font-size: 12px;
        }

        .payment-info {
            margin: 8px 0;
        }

        .footer-text {
            font-size: 10px;
            margin: 2px 0;
        }

        .final-spacing {
            margin-top: 12px;
        }

        .controls {
            margin: 20px 0;
            text-align: center;
        }

        .controls button {
            margin: 0 10px;
            padding: 10px 20px;
            font-size: 14px;
            cursor: pointer;
            border: 1px solid #ccc;
            background: #f0f0f0;
            border-radius: 4px;
        }

        .controls button:hover {
            background: #e0e0e0;
        }
    </style>
</head>
<body>
    <div>
        <div class="controls">
            <button onclick="window.print()">🖨️ Imprimir Recibo</button>
            <button onclick="location.reload()">🔄 Recargar</button>
        </div>
        <div id="receipt-container">
            <!-- Receipt will be generated here -->
        </div>
    </div>
</body>
</html>
