import { Transaction } from '@/interfaces/transaction';
import { stringToLongDateFormatted } from './date-operations';
import { numberToCurrencyString } from './string-operations';

export interface ReceiptData {
  transaction: Transaction;
  businessName?: string;
  businessAddress?: string;
  businessPhone?: string;
}

export const formatThermalReceipt = (receiptData: ReceiptData): string => {
  const { transaction, businessName = 'Propaga', businessAddress, businessPhone } = receiptData;

  const RECEIPT_WIDTH = 32;
  const SEPARATOR = '='.repeat(RECEIPT_WIDTH);
  const DASH_LINE = '-'.repeat(RECEIPT_WIDTH);

  const centerText = (text: string): string => {
    if (text.length >= RECEIPT_WIDTH) return text.substring(0, RECEIPT_WIDTH);
    const padding = Math.floor((RECEIPT_WIDTH - text.length) / 2);
    return ' '.repeat(padding) + text;
  };

  const formatTwoColumns = (left: string, right: string): string => {
    const maxLeftLength = RECEIPT_WIDTH - right.length;
    const leftTruncated =
      left.length > maxLeftLength ? left.substring(0, maxLeftLength - 3) + '...' : left;
    const padding = RECEIPT_WIDTH - leftTruncated.length - right.length;
    return leftTruncated + ' '.repeat(Math.max(0, padding)) + right;
  };

  let receipt = '';

  receipt += '\n';
  receipt += centerText(businessName) + '\n';
  if (businessAddress) {
    receipt += centerText(businessAddress) + '\n';
  }
  if (businessPhone) {
    receipt += centerText(businessPhone) + '\n';
  }
  receipt += '\n';
  receipt += centerText('RECIBO DE TRANSACCION') + '\n';
  receipt += SEPARATOR + '\n';
  receipt += '\n';

  receipt += formatTwoColumns('Referencia:', transaction.wholesalerTransactionId) + '\n';
  receipt += formatTwoColumns('Fecha:', stringToLongDateFormatted(transaction.movementDate)) + '\n';
  receipt += '\n';
  receipt += DASH_LINE + '\n';
  receipt += centerText('DETALLE DE COMPRA') + '\n';
  receipt += DASH_LINE + '\n';
  receipt;

  receipt +=
    formatTwoColumns('Monto base:', numberToCurrencyString(transaction.totalAmount)) + '\n';

  if (transaction.interests > 0) {
    receipt += formatTwoColumns('Intereses:', numberToCurrencyString(transaction.interests)) + '\n';
  }

  if (transaction.IVAAmount > 0) {
    receipt += formatTwoColumns('IVA:', numberToCurrencyString(transaction.IVAAmount)) + '\n';
  }

  receipt += DASH_LINE + '\n';
  receipt +=
    formatTwoColumns(
      'TOTAL:',
      numberToCurrencyString(transaction.totalAmountWithInterests || transaction.totalAmount),
    ) + '\n';
  receipt += SEPARATOR + '\n';
  receipt += '\n';

  receipt += centerText('INFORMACION DE PAGO') + '\n';
  receipt += DASH_LINE + '\n';
  receipt += formatTwoColumns('Plazo:', '15 dias') + '\n';
  receipt +=
    formatTwoColumns('Fecha limite:', stringToLongDateFormatted(transaction.paymentDate)) + '\n';
  receipt += '\n';
  receipt += centerText('El cliente pagará después') + '\n';
  receipt += centerText('en el portal de Pagos de Propaga') + '\n';
  receipt += '\n';

  receipt += SEPARATOR + '\n';
  receipt += centerText('Gracias por su compra') + '\n';
  receipt += centerText('Conserve este recibo') + '\n';
  receipt += '\n';
  receipt += '\n';
  receipt += '\n';

  return receipt;
};

export const formatHtmlReceipt = (receiptData: ReceiptData): string => {
  const { transaction, businessName = 'Propaga', businessAddress, businessPhone } = receiptData;

  const centerText = (text: string): string => {
    return `<div class="center-text">${text}</div>`;
  };

  const formatTwoColumns = (left: string, right: string): string => {
    return `<div class="two-columns"><span class="left-col">${left}</span><span class="right-col">${right}</span></div>`;
  };

  const separator = '<div class="separator">================================</div>';
  const dashLine = '<div class="dash-line">--------------------------------</div>';

  let receiptHtml = `
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recibo Térmico</title>
    <style>
        @media print {
            @page {
                size: 58mm auto;
                margin: 0;
            }
            body {
                margin: 0;
                padding: 0;
            }
            .receipt-container {
                width: 58mm !important;
                max-width: 58mm !important;
                margin: 0 !important;
                padding: 2mm !important;
                box-shadow: none !important;
                border: none !important;
            }
        }

        body {
            margin: 0;
            padding: 20px;
            background-color: #e8e8e8;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
        }

        .receipt-container {
            width: 320px;
            max-width: 320px;
            background: linear-gradient(135deg, #f8f8f8 0%, #f5f5f5 100%);
            border: 2px dotted #ccc;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }

        .receipt-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 1px 1px, rgba(0,0,0,0.02) 1px, transparent 0);
            background-size: 8px 8px;
            pointer-events: none;
        }

        .receipt-content {
            position: relative;
            z-index: 1;
            font-size: 11px;
            line-height: 1.1;
            color: #2a2a2a;
            letter-spacing: 0.3px;
        }

        .center-text {
            text-align: center;
            margin: 2px 0;
            font-weight: 500;
        }

        .two-columns {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 1px 0;
            min-height: 12px;
        }

        .left-col {
            flex: 1;
            text-align: left;
            padding-right: 4px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .right-col {
            text-align: right;
            white-space: nowrap;
            font-weight: 600;
        }

        .separator {
            text-align: center;
            margin: 4px 0;
            font-weight: bold;
            letter-spacing: -0.5px;
            color: #444;
        }

        .dash-line {
            text-align: center;
            margin: 3px 0;
            color: #666;
            letter-spacing: -0.5px;
        }

        .section-spacing {
            margin: 8px 0;
        }

        .business-info {
            margin-bottom: 8px;
        }

        .business-info .center-text {
            font-weight: 600;
            font-size: 12px;
        }

        .transaction-title {
            font-weight: bold;
            font-size: 11px;
            margin: 6px 0;
        }

        .section-title {
            font-weight: bold;
            font-size: 10px;
            margin: 4px 0;
        }

        .total-row {
            font-weight: bold;
            font-size: 12px;
        }

        .payment-info {
            margin: 8px 0;
        }

        .footer-text {
            font-size: 10px;
            margin: 2px 0;
        }

        .final-spacing {
            margin-top: 12px;
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <div class="receipt-content">`;

  // Business header
  receiptHtml += '<div class="business-info">';
  receiptHtml += '<div style="margin: 4px 0;"></div>';
  receiptHtml += centerText(businessName);
  if (businessAddress) {
    receiptHtml += centerText(businessAddress);
  }
  if (businessPhone) {
    receiptHtml += centerText(businessPhone);
  }
  receiptHtml += '<div style="margin: 4px 0;"></div>';
  receiptHtml += '<div class="transaction-title">' + centerText('RECIBO DE TRANSACCION') + '</div>';
  receiptHtml += separator;
  receiptHtml += '<div style="margin: 4px 0;"></div>';
  receiptHtml += '</div>';

  // Transaction details
  receiptHtml += formatTwoColumns('Referencia:', transaction.wholesalerTransactionId);
  receiptHtml += formatTwoColumns('Fecha:', stringToLongDateFormatted(transaction.movementDate));
  receiptHtml += '<div style="margin: 4px 0;"></div>';
  receiptHtml += dashLine;
  receiptHtml += '<div class="section-title">' + centerText('DETALLE DE COMPRA') + '</div>';
  receiptHtml += dashLine;

  // Purchase details
  receiptHtml += formatTwoColumns('Monto base:', numberToCurrencyString(transaction.totalAmount));

  if (transaction.interests > 0) {
    receiptHtml += formatTwoColumns('Intereses:', numberToCurrencyString(transaction.interests));
  }

  if (transaction.IVAAmount > 0) {
    receiptHtml += formatTwoColumns('IVA:', numberToCurrencyString(transaction.IVAAmount));
  }

  receiptHtml += dashLine;
  receiptHtml +=
    '<div class="total-row">' +
    formatTwoColumns(
      'TOTAL:',
      numberToCurrencyString(transaction.totalAmountWithInterests || transaction.totalAmount),
    ) +
    '</div>';
  receiptHtml += separator;
  receiptHtml += '<div style="margin: 4px 0;"></div>';

  // Payment information
  receiptHtml += '<div class="payment-info">';
  receiptHtml += '<div class="section-title">' + centerText('INFORMACION DE PAGO') + '</div>';
  receiptHtml += dashLine;
  receiptHtml += formatTwoColumns('Plazo:', '15 dias');
  receiptHtml += formatTwoColumns(
    'Fecha limite:',
    stringToLongDateFormatted(transaction.paymentDate),
  );
  receiptHtml += '<div style="margin: 4px 0;"></div>';
  receiptHtml += '<div class="footer-text">' + centerText('El cliente pagará después') + '</div>';
  receiptHtml +=
    '<div class="footer-text">' + centerText('en el portal de Pagos de Propaga') + '</div>';
  receiptHtml += '<div style="margin: 4px 0;"></div>';
  receiptHtml += '</div>';

  // Footer
  receiptHtml += separator;
  receiptHtml += '<div class="footer-text">' + centerText('Gracias por su compra') + '</div>';
  receiptHtml += '<div class="footer-text">' + centerText('Conserve este recibo') + '</div>';
  receiptHtml += '<div class="final-spacing"></div>';

  receiptHtml += `
        </div>
    </div>
</body>
</html>`;

  return receiptHtml;
};

export const formatReceiptPreview = (receiptData: ReceiptData): string => {
  const thermalReceipt = formatThermalReceipt(receiptData);
  return `<pre style="font-family: 'Courier New', monospace; font-size: 12px; line-height: 1.2; white-space: pre-wrap;">${thermalReceipt}</pre>`;
};
